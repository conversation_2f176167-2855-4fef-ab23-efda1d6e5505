/**
 * ============================================================================
 * SNAP CHARTS - Data Loader Utility
 * ============================================================================
 * 
 * Utility for loading test data from external JSON files instead of 
 * hardcoded data in the chart components.
 * 
 * @version 1.0.0
 * <AUTHOR> for MOD Team
 */

class SnapDataLoader {
  constructor() {
    this.dataCache = new Map();
    this.baseUrl = './'; // Relative to the chart component location
  }

  /**
   * Load dummy data from external JavaScript module
   * @param {string} dataSource - Data source identifier (not used, for compatibility)
   * @returns {Promise<Object>} The loaded data object
   */
  async loadDummyData(dataSource = 'default') {
    if (this.dataCache.has(dataSource)) {
      return this.dataCache.get(dataSource);
    }

    try {
      // Check if the data is available in the global scope
      if (typeof window !== 'undefined' && window.SnapDummyData) {
        const data = window.SnapDummyData;
        this.dataCache.set(dataSource, data);
        return data;
      } else {
        throw new Error('SnapDummyData not loaded. Make sure dummy-data.js is included before data-loader.js');
      }
    } catch (error) {
      console.error('Error loading dummy data:', error);
      throw error;
    }
  }

  /**
   * Get template structure for a specific chart type
   * @param {string} chartType - Type of chart (daily-sales-history, stacked-column, etc.)
   * @returns {Promise<Object>} Template structure
   */
  async getTemplate(chartType) {
    const data = await this.loadDummyData();
    return data.templates[chartType] || data.templates['stacked-column'];
  }

  /**
   * Get template structure only (no sample data)
   * @param {string} chartType - Type of chart
   * @returns {Promise<Object>} Template structure
   */
  async getTemplateStructure(chartType) {
    const template = await this.getTemplate(chartType);
    return template.structure;
  }

  /**
   * Get test data for a specific chart type
   * @param {string} dataType - Type of test data (stackedColumn, pieCharts, etc.)
   * @param {string} subType - Sub-type for pie charts (fitType, soldColors, etc.)
   * @returns {Promise<Array>} Test data array
   */
  async getTestData(dataType, subType = null) {
    const data = await this.loadDummyData();
    
    if (subType && data.testData[dataType] && data.testData[dataType][subType]) {
      return data.testData[dataType][subType];
    }
    
    return data.testData[dataType] || [];
  }

  /**
   * Get pie chart data by category
   * @param {string} category - Category name (fitType, soldColors, etc.)
   * @returns {Promise<Array>} Pie chart data
   */
  async getPieChartData(category) {
    return this.getTestData('pieCharts', category);
  }

  /**
   * Generate daily sales history data
   * @param {Object} options - Generation options
   * @param {string} options.startDate - Start date (YYYY-MM-DD)
   * @param {string} options.endDate - End date (YYYY-MM-DD)
   * @param {number} options.salesMin - Minimum sales value
   * @param {number} options.salesMax - Maximum sales value
   * @returns {Promise<Array>} Generated daily sales data
   */
  async generateDailySalesHistory(options = {}) {
    const data = await this.loadDummyData();
    const config = data.generators.dailySalesHistory;
    
    const settings = {
      startDate: options.startDate || config.parameters.startDate,
      endDate: options.endDate || config.parameters.endDate,
      salesMin: options.salesMin || config.parameters.salesRange.min,
      salesMax: options.salesMax || config.parameters.salesRange.max,
      royaltiesMultiplier: options.royaltiesMultiplier || config.parameters.royaltiesMultiplier,
      zeroSalesProbability: options.zeroSalesProbability || config.parameters.zeroSalesProbability,
      consecutiveZeroWeeks: options.consecutiveZeroWeeks || config.parameters.consecutiveZeroWeeks || 1
    };

    const startDate = new Date(settings.startDate);
    const endDate = new Date(settings.endDate);
    const generatedData = [];
    
    // Calculate total days and determine when to place consecutive zero weeks
    const totalDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
    const zeroWeekStartDay = Math.floor(Math.random() * (totalDays - 7 * settings.consecutiveZeroWeeks));
    const zeroWeekEndDay = zeroWeekStartDay + (7 * settings.consecutiveZeroWeeks);
    
    // Track months that have high returns days (13-21 returns)
    const monthsWithHighReturns = new Set();
    
    // First pass: Generate all data with normal returns logic
    let dayIndex = 0;
    const allDatesInRange = [];

    for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
      const currentDate = new Date(date);
      let isZeroSales = false;
      
      // Check if this day falls within the consecutive zero weeks
      if (dayIndex >= zeroWeekStartDay && dayIndex < zeroWeekEndDay) {
        isZeroSales = true;
      } else {
        // Apply normal zero sales probability for other days
        isZeroSales = Math.random() < settings.zeroSalesProbability;
      }
      
      const sales = isZeroSales ? 0 : Math.floor(Math.random() * (settings.salesMax - settings.salesMin + 1)) + settings.salesMin;
      const royalties = Math.floor(sales * settings.royaltiesMultiplier);
      
      // Generate returns data (0-15% of sales, with 70% probability of 0 returns)
      let returns = 0;
      if (sales > 0 && Math.random() > 0.7) {
        returns = Math.floor(sales * (Math.random() * 0.15)); // 0-15% of sales
      } else if (sales === 0 && Math.random() < 0.05) {
        // 5% chance of returns-only day (1-3 returns)
        returns = Math.floor(Math.random() * 3) + 1;
      }

      const dataPoint = {
        month: currentDate.toLocaleDateString('en-US', { month: 'short' }).toUpperCase(),
        day: currentDate.getDate().toString().padStart(2, '0'),
        year: currentDate.getFullYear().toString().slice(-2),
        sales: sales,
        royalties: royalties,
        returns: returns,
        fullDate: currentDate.toISOString().split('T')[0],
        dateObj: currentDate.toISOString(),
        monthKey: `${currentDate.getFullYear()}-${currentDate.getMonth()}`, // Track month for high returns
        originalDate: new Date(currentDate) // Store original date for processing
      };

      generatedData.push(dataPoint);
      allDatesInRange.push(dataPoint);
      
      dayIndex++;
    }

    // Second pass: Ensure each month has at least one day with 13-21 returns
    const monthGroups = {};
    
    // Group data points by month
    generatedData.forEach(dataPoint => {
      const monthKey = dataPoint.monthKey;
      if (!monthGroups[monthKey]) {
        monthGroups[monthKey] = [];
      }
      monthGroups[monthKey].push(dataPoint);
    });

    // For each month, ensure at least one day has 13-21 returns
    Object.keys(monthGroups).forEach(monthKey => {
      const monthData = monthGroups[monthKey];
      
      // Check if any day in this month already has 13-21 returns
      const hasHighReturns = monthData.some(day => day.returns >= 13 && day.returns <= 21);
      
      if (!hasHighReturns) {
        // Pick a random day in this month to assign high returns
        const randomDayIndex = Math.floor(Math.random() * monthData.length);
        const selectedDay = monthData[randomDayIndex];
        
        // Assign 13-21 returns to this day
        selectedDay.returns = Math.floor(Math.random() * 9) + 13; // Random number between 13-21
        
        // Optional: Ensure this day has some sales to make it more realistic
        // If the selected day has 0 sales, give it modest sales
        if (selectedDay.sales === 0) {
          selectedDay.sales = Math.floor(Math.random() * 20) + 10; // 10-29 sales
          selectedDay.royalties = Math.floor(selectedDay.sales * settings.royaltiesMultiplier);
        }
        
        monthsWithHighReturns.add(monthKey);
      }
    });

    // Remove temporary properties used for processing
    generatedData.forEach(dataPoint => {
      delete dataPoint.monthKey;
      delete dataPoint.originalDate;
    });

    return generatedData;
  }

  /**
   * Generate stacked column data
   * @param {Object} options - Generation options
   * @param {number} options.days - Number of days to generate
   * @param {Array} options.marketplaces - Array of marketplace codes
   * @returns {Promise<Array>} Generated stacked column data
   */
  async generateStackedColumnData(options = {}) {
    const data = await this.loadDummyData();
    const config = data.generators.stackedColumn;
    
    const settings = {
      days: options.days || config.parameters.dateRange,
      marketplaces: options.marketplaces || config.parameters.marketplaces,
      salesMin: options.salesMin || config.parameters.salesRange.min,
      salesMax: options.salesMax || config.parameters.salesRange.max,
      royaltiesMultiplier: options.royaltiesMultiplier || config.parameters.royaltiesMultiplier
    };

    const generatedData = [];
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - settings.days);

    for (let i = 0; i < settings.days; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(currentDate.getDate() + i);

      // Generate individual marketplace data
      const marketplaces = [];
      let totalSales = 0;
      let totalRoyalties = 0;
      let totalReturns = 0;
      
      // Determine which marketplace should have 0 returns (cycle through marketplaces)
      const zeroReturnsMarketplaceIndex = i % settings.marketplaces.length;
      
      settings.marketplaces.forEach((marketplaceCode, index) => {
        // Generate individual marketplace sales
        const salesMin = Math.floor(settings.salesMin / settings.marketplaces.length);
        const salesMax = Math.floor(settings.salesMax / settings.marketplaces.length);
        const sales = Math.floor(Math.random() * (salesMax - salesMin + 1)) + salesMin;
        
        // Calculate individual marketplace royalties
        const royalties = Math.floor(sales * settings.royaltiesMultiplier);
        
        // Calculate individual marketplace returns (10% of sales, but one marketplace has 0)
        const returns = (index === zeroReturnsMarketplaceIndex) ? 0 : Math.floor(sales * 0.1);
        
        marketplaces.push({
          code: marketplaceCode,
          sales: sales,
          royalties: royalties,
          returns: returns
        });
        
        totalSales += sales;
        totalRoyalties += royalties;
        totalReturns += returns;
      });
      
      // Create backward compatibility arrays
      const values = marketplaces.map(m => m.sales);
      const labels = marketplaces.map(m => m.code);

      generatedData.push({
        month: currentDate.toLocaleDateString('en-US', { month: 'short' }).toUpperCase(),
        day: currentDate.getDate().toString().padStart(2, '0'),
        year: currentDate.getFullYear().toString().slice(-2),
        marketplaces: marketplaces,
        sales: totalSales,
        royalties: totalRoyalties,
        returns: totalReturns,
        change: totalReturns, // For backward compatibility
        values: values,
        labels: labels,
        fullDate: currentDate.toISOString().split('T')[0],
        dateObj: currentDate.toISOString(),
        zeroReturnsMarketplaceIndex: zeroReturnsMarketplaceIndex // Store which marketplace has 0 returns
      });
    }

    return generatedData;
  }

  /**
   * Clear data cache
   */
  clearCache() {
    this.dataCache.clear();
  }

  /**
   * Get formatted template string for data editor
   * @param {string} chartType - Type of chart
   * @returns {Promise<string>} Formatted JSON template string
   */
  async getTemplateString(chartType) {
    const template = await this.getTemplate(chartType);
    
    // Return structure as formatted JSON for data editor
    if (template.sample && template.sample.length > 0) {
      return JSON.stringify(template.sample, null, 2);
    }
    
    // Return single structure item wrapped in array
    return JSON.stringify([template.structure], null, 2);
  }
}

// Create global instance
window.SnapDataLoader = window.SnapDataLoader || new SnapDataLoader();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SnapDataLoader;
} 