<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Snap Charts - Comparison Data Test</title>
    <link rel="stylesheet" href="snap-charts.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        .chart-container {
            height: 400px;
            margin: 20px 0;
        }
        .data-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
            font-size: 14px;
            line-height: 1.5;
        }
        .marketplace-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 8px;
        }
        .marketplace-tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .comparison-tag {
            background: #fff3e0;
            color: #f57c00;
        }
        .refresh-btn {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .refresh-btn:hover {
            background: #1976d2;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Snap Charts - Comparison Data Test</h1>
        <p>This test demonstrates the new comparison data generation with varied marketplace patterns and independent random values.</p>
        
        <button class="refresh-btn" onclick="regenerateData()">🔄 Regenerate Data</button>
        
        <div class="test-section">
            <div class="test-title">Dashboard-Style Comparison Chart</div>
            <div id="dashboardChart" class="chart-container"></div>
            <div id="dashboardInfo" class="data-info"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">Data Loader Comparison Chart</div>
            <div id="dataLoaderChart" class="chart-container"></div>
            <div id="dataLoaderInfo" class="data-info"></div>
        </div>
    </div>

    <script src="dummy-data.js"></script>
    <script src="data-loader.js"></script>
    <script src="snap-charts.js"></script>
    <script>
        let dashboardChart, dataLoaderChart;
        
        // Dashboard-style data generation (like in dashboard.js)
        function generateDashboardStyleData() {
            const currentData = [];
            const comparisonData = [];
            
            // Helper function from dashboard.js (main data)
            function generateRandomDaySales() {
                const allMarketplaces = ["US", "UK", "DE", "FR", "IT", "ES", "JP"];
                const numActiveMarketplaces = Math.floor(Math.random() * 7) + 1;
                const shuffled = [...allMarketplaces].sort(() => 0.5 - Math.random());
                const activeMarketplaces = shuffled.slice(0, numActiveMarketplaces);
                
                const salesData = {};
                allMarketplaces.forEach(marketplace => {
                    if (activeMarketplaces.includes(marketplace)) {
                        const sales = Math.floor(Math.random() * 91) + 5;
                        salesData[marketplace] = {
                            sales: sales,
                            returns: Math.random() < 0.7 ? 0 : Math.floor(Math.random() * Math.max(1, sales * 0.1))
                        };
                    } else {
                        salesData[marketplace] = { sales: 0, returns: 0 };
                    }
                });
                return salesData;
            }
            
            // Helper function from dashboard.js (comparison data)
            function generateRandomComparisonDaySales() {
                const allMarketplaces = ["US", "UK", "DE", "FR", "IT", "ES", "JP", "Walmart", "TikTok", "Shopify"];
                const numActiveMarketplaces = Math.floor(Math.random() * 6) + 3;
                const shuffled = [...allMarketplaces].sort(() => 0.5 - Math.random());
                const activeMarketplaces = shuffled.slice(0, numActiveMarketplaces);
                
                const salesData = {};
                ["US", "UK", "DE", "FR", "IT", "ES", "JP"].forEach(marketplace => {
                    salesData[marketplace] = { sales: 0, returns: 0 };
                });
                
                activeMarketplaces.forEach(marketplace => {
                    if (["US", "UK", "DE", "FR", "IT", "ES", "JP"].includes(marketplace)) {
                        const sales = Math.floor(Math.random() * 113) + 8;
                        salesData[marketplace] = {
                            sales: sales,
                            returns: Math.random() < 0.65 ? 0 : Math.floor(Math.random() * Math.max(1, sales * 0.12))
                        };
                    }
                });
                return salesData;
            }
            
            // Generate 7 days of data
            for (let i = 6; i >= 0; i--) {
                const currentDate = new Date();
                currentDate.setDate(currentDate.getDate() - i);
                
                // Main data
                const mainSales = generateRandomDaySales();
                const mainMarketplaces = [
                    { code: "US", sales: mainSales.US.sales, royalties: Math.floor(mainSales.US.sales * 0.35), returns: mainSales.US.returns },
                    { code: "UK", sales: mainSales.UK.sales, royalties: Math.floor(mainSales.UK.sales * 0.35), returns: mainSales.UK.returns },
                    { code: "DE", sales: mainSales.DE.sales, royalties: Math.floor(mainSales.DE.sales * 0.35), returns: mainSales.DE.returns },
                    { code: "FR", sales: mainSales.FR.sales, royalties: Math.floor(mainSales.FR.sales * 0.35), returns: mainSales.FR.returns },
                    { code: "IT", sales: mainSales.IT.sales, royalties: Math.floor(mainSales.IT.sales * 0.35), returns: mainSales.IT.returns },
                    { code: "ES", sales: mainSales.ES.sales, royalties: Math.floor(mainSales.ES.sales * 0.35), returns: mainSales.ES.returns },
                    { code: "JP", sales: mainSales.JP.sales, royalties: Math.floor(mainSales.JP.sales * 0.35), returns: mainSales.JP.returns }
                ];
                
                currentData.push({
                    month: currentDate.toLocaleDateString('en-US', { month: 'short' }).toUpperCase(),
                    day: currentDate.getDate().toString().padStart(2, '0'),
                    year: currentDate.getFullYear().toString().slice(-2),
                    marketplaces: mainMarketplaces,
                    sales: mainMarketplaces.reduce((sum, mp) => sum + mp.sales, 0),
                    royalties: mainMarketplaces.reduce((sum, mp) => sum + mp.royalties, 0),
                    returns: mainMarketplaces.reduce((sum, mp) => sum + mp.returns, 0),
                    values: mainMarketplaces.map(mp => mp.sales),
                    labels: mainMarketplaces.map(mp => mp.code)
                });
                
                // Comparison data
                const compSales = generateRandomComparisonDaySales();
                const compMarketplaces = [
                    { code: "US", sales: compSales.US.sales, royalties: Math.floor(compSales.US.sales * 0.35), returns: compSales.US.returns },
                    { code: "UK", sales: compSales.UK.sales, royalties: Math.floor(compSales.UK.sales * 0.35), returns: compSales.UK.returns },
                    { code: "DE", sales: compSales.DE.sales, royalties: Math.floor(compSales.DE.sales * 0.35), returns: compSales.DE.returns },
                    { code: "FR", sales: compSales.FR.sales, royalties: Math.floor(compSales.FR.sales * 0.35), returns: compSales.FR.returns },
                    { code: "IT", sales: compSales.IT.sales, royalties: Math.floor(compSales.IT.sales * 0.35), returns: compSales.IT.returns },
                    { code: "ES", sales: compSales.ES.sales, royalties: Math.floor(compSales.ES.sales * 0.35), returns: compSales.ES.returns },
                    { code: "JP", sales: compSales.JP.sales, royalties: Math.floor(compSales.JP.sales * 0.35), returns: compSales.JP.returns }
                ];
                
                comparisonData.push({
                    month: currentDate.toLocaleDateString('en-US', { month: 'short' }).toUpperCase(),
                    day: currentDate.getDate().toString().padStart(2, '0'),
                    year: currentDate.getFullYear().toString().slice(-2),
                    marketplaces: compMarketplaces,
                    sales: compMarketplaces.reduce((sum, mp) => sum + mp.sales, 0),
                    royalties: compMarketplaces.reduce((sum, mp) => sum + mp.royalties, 0),
                    returns: compMarketplaces.reduce((sum, mp) => sum + mp.returns, 0),
                    values: compMarketplaces.map(mp => mp.sales),
                    labels: compMarketplaces.map(mp => mp.code)
                });
            }
            
            return { currentData, comparisonData };
        }
        
        // Data loader style data generation
        async function generateDataLoaderStyleData() {
            const dataLoader = new SnapDataLoader();
            
            // Generate main data
            const mainData = await dataLoader.generateStackedColumnData({ days: 7 });
            
            // Generate comparison data with new method
            const comparisonData = await dataLoader.generateComparisonStackedColumnData({ days: 7 });
            
            return { currentData: mainData, comparisonData };
        }
        
        function displayDataInfo(containerId, mainData, comparisonData, title) {
            const container = document.getElementById(containerId);
            
            // Analyze main data marketplaces
            const mainMarketplaces = new Set();
            mainData.forEach(day => {
                day.marketplaces.forEach(mp => {
                    if (mp.sales > 0) mainMarketplaces.add(mp.code);
                });
            });
            
            // Analyze comparison data marketplaces
            const compMarketplaces = new Set();
            comparisonData.forEach(day => {
                day.marketplaces.forEach(mp => {
                    if (mp.sales > 0) compMarketplaces.add(mp.code);
                });
            });
            
            container.innerHTML = `
                <strong>${title}</strong><br>
                <strong>Main Data:</strong> ${mainMarketplaces.size} unique marketplaces
                <div class="marketplace-list">
                    ${Array.from(mainMarketplaces).map(mp => `<span class="marketplace-tag">${mp}</span>`).join('')}
                </div>
                <strong>Comparison Data:</strong> ${compMarketplaces.size} unique marketplaces
                <div class="marketplace-list">
                    ${Array.from(compMarketplaces).map(mp => `<span class="marketplace-tag comparison-tag">${mp}</span>`).join('')}
                </div>
                <br>
                <strong>Key Differences:</strong>
                <ul>
                    <li>Independent marketplace selection for comparison data</li>
                    <li>Different value ranges and patterns</li>
                    <li>Varied marketplace counts per day</li>
                    <li>Realistic simulation of different time periods</li>
                </ul>
            `;
        }
        
        async function initializeCharts() {
            // Dashboard style chart
            const dashboardData = generateDashboardStyleData();
            dashboardChart = new SnapChart({
                container: '#dashboardChart',
                type: 'stacked-column',
                data: dashboardData.currentData,
                options: {
                    title: 'Dashboard Style Comparison',
                    compareMode: true,
                    compareData: dashboardData.comparisonData,
                    animate: true,
                    responsive: true
                }
            });
            
            displayDataInfo('dashboardInfo', dashboardData.currentData, dashboardData.comparisonData, 'Dashboard Style Generation');
            
            // Data loader style chart
            const dataLoaderData = await generateDataLoaderStyleData();
            dataLoaderChart = new SnapChart({
                container: '#dataLoaderChart',
                type: 'stacked-column',
                data: dataLoaderData.currentData,
                options: {
                    title: 'Data Loader Style Comparison',
                    compareMode: true,
                    compareData: dataLoaderData.comparisonData,
                    animate: true,
                    responsive: true
                }
            });
            
            displayDataInfo('dataLoaderInfo', dataLoaderData.currentData, dataLoaderData.comparisonData, 'Data Loader Style Generation');
        }
        
        async function regenerateData() {
            await initializeCharts();
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', initializeCharts);
    </script>
</body>
</html>
